'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

// Cache global para dados
const globalCache = new Map();

// Configurações de cache
const CACHE_CONFIG = {
  defaultTTL: 5 * 60 * 1000, // 5 minutos
  maxSize: 100, // Máximo de 100 itens no cache
  cleanupInterval: 10 * 60 * 1000 // Limpeza a cada 10 minutos
};

class DataCache {
  constructor() {
    this.cache = globalCache;
    this.cleanupTimer = null;
    this.startCleanupTimer();
  }

  // Gerar chave de cache
  generateKey(key, params = {}) {
    const paramsStr = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null)
      .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
      .map(([key, value]) => `${key}:${value}`)
      .join('|');

    return `${key}:${paramsStr || 'default'}`;
  }

  // Obter dados do cache
  get(key, params = {}) {
    const cacheKey = this.generateKey(key, params);
    const item = this.cache.get(cacheKey);

    if (!item) return null;

    // Verificar se expirou
    if (Date.now() > item.expiresAt) {
      this.cache.delete(cacheKey);
      return null;
    }

    return item.data;
  }

  // Armazenar dados no cache
  set(key, data, params = {}, ttl = CACHE_CONFIG.defaultTTL) {
    const cacheKey = this.generateKey(key, params);
    
    // Limpar cache se exceder o tamanho máximo
    if (this.cache.size >= CACHE_CONFIG.maxSize) {
      this.cleanup();
    }

    this.cache.set(cacheKey, {
      data,
      expiresAt: Date.now() + ttl,
      createdAt: Date.now()
    });
  }

  // Invalidar cache
  invalidate(pattern) {
    for (const [key] of this.cache) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  // Limpar cache expirado
  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache) {
      if (now > item.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  // Iniciar timer de limpeza
  startCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, CACHE_CONFIG.cleanupInterval);
  }

  // Parar timer de limpeza
  stopCleanupTimer() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }
}

// Instância global do cache
const dataCache = new DataCache();

// Hook para usar cache de dados
export function useDataCache(key, fetchFunction, options = {}) {
  const {
    params = {},
    ttl = CACHE_CONFIG.defaultTTL,
    enabled = true,
    refetchOnMount = false,
    refetchOnWindowFocus = false
  } = options;

  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetched, setLastFetched] = useState(null);

  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!enabled || !fetchFunction) return;

    try {
      setIsLoading(true);
      setError(null);

      // Verificar cache se não for refresh forçado
      if (!forceRefresh) {
        const cachedData = dataCache.get(key, params);
        if (cachedData) {
          setData(cachedData);
          setLastFetched(Date.now());
          return;
        }
      }

      // Buscar dados
      const result = await fetchFunction(params);
      
      // Armazenar no cache
      dataCache.set(key, result, params, ttl);
      
      setData(result);
      setLastFetched(Date.now());
    } catch (err) {
      console.error(`Erro ao buscar dados para ${key}:`, err);
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, [key, fetchFunction, params, ttl, enabled]);

  // Carregar dados na montagem
  useEffect(() => {
    if (enabled) {
      fetchData(refetchOnMount);
    }
  }, [enabled, refetchOnMount, fetchData]);

  // Refetch quando a janela ganha foco
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      fetchData(true);
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, fetchData]);

  // Função para invalidar cache
  const invalidateCache = useCallback(() => {
    dataCache.invalidate(key);
  }, [key]);

  // Função para refetch
  const refetch = useCallback(() => {
    fetchData(true);
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    lastFetched,
    refetch,
    invalidateCache
  };
}

// Hook para cache de múltiplos dados
export function useMultiDataCache(queries) {
  const results = queries.map(({ key, fetchFunction, options }) => 
    useDataCache(key, fetchFunction, options)
  );

  const isLoading = results.some(result => result.isLoading);
  const hasError = results.some(result => result.error);

  const refetchAll = useCallback(() => {
    results.forEach(result => result.refetch());
  }, [results]);

  const invalidateAll = useCallback(() => {
    results.forEach(result => result.invalidateCache());
  }, [results]);

  return {
    results,
    isLoading,
    hasError,
    refetchAll,
    invalidateAll
  };
}

// Utilitário para prefetch de dados
export const prefetchData = async (key, fetchFunction, params = {}, ttl = CACHE_CONFIG.defaultTTL) => {
  try {
    const result = await fetchFunction(params);
    dataCache.set(key, result, params, ttl);
    return result;
  } catch (error) {
    console.error(`Erro no prefetch de ${key}:`, error);
    throw error;
  }
};

// Utilitário para limpar cache
export const clearCache = (pattern = null) => {
  if (pattern) {
    dataCache.invalidate(pattern);
  } else {
    globalCache.clear();
  }
};

export default dataCache; 