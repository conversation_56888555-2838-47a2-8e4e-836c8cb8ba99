{"timestamp":"2025-06-18 15:53:39","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Wed Jun 18 2025 15:53:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":139341824,"heapTotal":45232128,"heapUsed":43514688,"external":3406239,"arrayBuffers":132126}},"os":{"loadavg":[1.92,1.6,1.62],"uptime":4462.72},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-24 20:14:25","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Tue Jun 24 2025 20:14:25 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":140279808,"heapTotal":47067136,"heapUsed":45005232,"external":3429355,"arrayBuffers":155242}},"os":{"loadavg":[1.79,1.91,1.12],"uptime":640.52},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-24 20:40:15","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Tue Jun 24 2025 20:40:15 GMT+0000 (Coordinated Universal Time)","process":{"pid":139,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":133931008,"heapTotal":42610688,"heapUsed":41116104,"external":3384286,"arrayBuffers":110173}},"os":{"loadavg":[1.2,1.63,1.62],"uptime":2190.63},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-06-24 21:17:44","level":"error","message":"uncaughtException: Cannot find module '../../middlewares/auth'\nRequire stack:\n- /usr/src/app/src/routes/modulePreferencesRoutes.js\n- /usr/src/app/src/server.js\nError: Cannot find module '../../middlewares/auth'\nRequire stack:\n- /usr/src/app/src/routes/modulePreferencesRoutes.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/routes/modulePreferencesRoutes.js:3:26)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","error":{"code":"MODULE_NOT_FOUND","requireStack":["/usr/src/app/src/routes/modulePreferencesRoutes.js","/usr/src/app/src/server.js"]},"stack":"Error: Cannot find module '../../middlewares/auth'\nRequire stack:\n- /usr/src/app/src/routes/modulePreferencesRoutes.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/routes/modulePreferencesRoutes.js:3:26)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","exception":true,"date":"Tue Jun 24 2025 21:17:44 GMT+0000 (Coordinated Universal Time)","process":{"pid":235,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":164761600,"heapTotal":105394176,"heapUsed":75418944,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[1.44,1.7,1.73],"uptime":4439.77},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1051,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":26,"file":"/usr/src/app/src/routes/modulePreferencesRoutes.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
{"timestamp":"2025-06-24 21:18:12","level":"error","message":"uncaughtException: Cannot find module '../../middlewares/auth'\nRequire stack:\n- /usr/src/app/src/routes/modulePreferencesRoutes.js\n- /usr/src/app/src/server.js\nError: Cannot find module '../../middlewares/auth'\nRequire stack:\n- /usr/src/app/src/routes/modulePreferencesRoutes.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/routes/modulePreferencesRoutes.js:3:26)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","error":{"code":"MODULE_NOT_FOUND","requireStack":["/usr/src/app/src/routes/modulePreferencesRoutes.js","/usr/src/app/src/server.js"]},"stack":"Error: Cannot find module '../../middlewares/auth'\nRequire stack:\n- /usr/src/app/src/routes/modulePreferencesRoutes.js\n- /usr/src/app/src/server.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Module._load (node:internal/modules/cjs/loader:1051:27)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/routes/modulePreferencesRoutes.js:3:26)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)","exception":true,"date":"Tue Jun 24 2025 21:18:12 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":160473088,"heapTotal":105394176,"heapUsed":75256824,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[1.03,1.58,1.69],"uptime":4468.41},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1051,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":26,"file":"/usr/src/app/src/routes/modulePreferencesRoutes.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false}]}
