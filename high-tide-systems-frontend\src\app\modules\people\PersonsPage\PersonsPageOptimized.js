'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { useOptimizedData, useParallelData } from '@/hooks/useOptimizedData';
import { LoadingSpinner, TableLoadingSkeleton, PageLoading } from '@/components/ui/OptimizedLoading';
import { ModuleHeader, ModuleTable, ModuleFormGroup, ModuleSelect } from '@/components/ui';
import { personsService } from '../services/personsService';
import { companyService } from '@/app/modules/admin/services/companyService';
import PersonFormModal from '../components/PersonFormModal';
import ConfirmationDialog from '@/components/ui/ConfirmationDialog';
import { 
  Plus, 
  Search, 
  Filter, 
  RefreshCw, 
  Edit, 
  Trash, 
  Eye,
  Download,
  Upload
} from 'lucide-react';

const PersonsPageOptimized = () => {
  const { user: currentUser } = useAuth();
  const router = useRouter();
  const { toast_success, toast_error } = useToast();

  // Estados de UI
  const [search, setSearch] = useState("");
  const [personsFilter, setPersonsFilter] = useState([]);
  const [statusFilter, setStatusFilter] = useState("");
  const [relationshipFilter, setRelationshipFilter] = useState("");
  const [companyFilter, setCompanyFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [personFormOpen, setPersonFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Constants
  const ITEMS_PER_PAGE = 10;

  // Hook otimizado para carregar pessoas
  const {
    data: personsData,
    isLoading: isLoadingPersons,
    error: personsError,
    refetch: refetchPersons
  } = useOptimizedData(
    'persons',
    async (params) => {
      const response = await personsService.getPersons({
        page: currentPage,
        limit: ITEMS_PER_PAGE,
        search: search || undefined,
        personIds: personsFilter.length > 0 ? personsFilter : undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        relationship: relationshipFilter || undefined,
        companyId: companyFilter || undefined,
      });
      return response;
    },
    {
      params: {
        page: currentPage,
        search,
        personsFilter,
        statusFilter,
        relationshipFilter,
        companyFilter
      },
      ttl: 2 * 60 * 1000, // 2 minutos
      refetchOnMount: false,
      dependencies: [currentPage, search, personsFilter, statusFilter, relationshipFilter, companyFilter]
    }
  );

  // Hook para carregamento paralelo de dados auxiliares
  const {
    results: auxiliaryData,
    isLoading: isLoadingAuxiliary,
    errors: auxiliaryErrors
  } = useParallelData([
    {
      key: 'companies',
      fetchFunction: async () => {
        if (currentUser?.role !== "SYSTEM_ADMIN") return [];
        return await companyService.getCompaniesForSelect();
      }
    },
    {
      key: 'personOptions',
      fetchFunction: async () => {
        const response = await personsService.getPersons({
          limit: 100,
          active: true
        });
        return response?.persons?.map(person => ({
          value: person.id,
          label: person.fullName,
          sortName: person.fullName.toLowerCase()
        })).sort((a, b) => a.sortName.localeCompare(b.sortName)) || [];
      }
    }
  ]);

  // Extrair dados
  const persons = personsData?.persons || personsData?.people || personsData?.data || [];
  const totalPersons = personsData?.total || 0;
  const totalPages = personsData?.pages || 1;
  const companies = auxiliaryData.companies || [];
  const personOptions = auxiliaryData.personOptions || [];

  // Handlers
  const handleSearch = useCallback((e) => {
    e.preventDefault();
    setCurrentPage(1);
    refetchPersons();
  }, [refetchPersons]);

  const handlePersonsFilterChange = useCallback((value) => {
    setPersonsFilter(value);
    setCurrentPage(1);
  }, []);

  const handleStatusFilterChange = useCallback((value) => {
    setStatusFilter(value);
    setCurrentPage(1);
  }, []);

  const handleRelationshipFilterChange = useCallback((value) => {
    setRelationshipFilter(value);
    setCurrentPage(1);
  }, []);

  const handleCompanyFilterChange = useCallback((value) => {
    setCompanyFilter(value);
    setCurrentPage(1);
  }, []);

  const handlePageChange = useCallback((page) => {
    setCurrentPage(page);
  }, []);

  const handleResetFilters = useCallback(() => {
    setSearch("");
    setPersonsFilter([]);
    setStatusFilter("");
    setRelationshipFilter("");
    setCompanyFilter("");
    setCurrentPage(1);
  }, []);

  const handleEditPerson = useCallback(async (person) => {
    try {
      const personData = await personsService.getPerson(person.id);
      setSelectedPerson(personData);
      setPersonFormOpen(true);
    } catch (error) {
      console.error('Erro ao buscar dados da pessoa:', error);
      setSelectedPerson(person);
      setPersonFormOpen(true);
    }
  }, []);

  const handleToggleStatus = useCallback((person) => {
    setSelectedPerson(person);
    setActionToConfirm({
      type: "toggle-status",
      message: `${person.active ? "Desativar" : "Ativar"} a pessoa ${person.fullName}?`,
    });
    setConfirmationDialogOpen(true);
  }, []);

  const handleDeletePerson = useCallback((person) => {
    setSelectedPerson(person);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente a pessoa ${person.fullName}?`,
    });
    setConfirmationDialogOpen(true);
  }, []);

  const confirmAction = useCallback(async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await personsService.togglePersonStatus(selectedPerson.id);
        refetchPersons();
        toast_success('Status da pessoa alterado com sucesso');
      } catch (error) {
        console.error("Erro ao alterar status da pessoa:", error);
        toast_error('Erro ao alterar status da pessoa');
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await personsService.deletePerson(selectedPerson.id);
        refetchPersons();
        toast_success('Pessoa excluída com sucesso');
      } catch (error) {
        console.error("Erro ao excluir pessoa:", error);
        toast_error('Erro ao excluir pessoa');
      }
    }
    setConfirmationDialogOpen(false);
  }, [actionToConfirm, selectedPerson, refetchPersons, toast_success, toast_error]);

  const handleExport = useCallback(async (format) => {
    setIsExporting(true);
    try {
      let companyName;
      if (companyFilter) {
        const selectedCompany = companies.find(c => c.id === companyFilter);
        companyName = selectedCompany ? selectedCompany.name : undefined;
      }

      await personsService.exportPersons({
        search: search || undefined,
        personIds: personsFilter.length > 0 ? personsFilter : undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        relationship: relationshipFilter || undefined,
        companyId: companyFilter || undefined,
        companyName
      }, format);

      toast_success('Exportação realizada com sucesso');
    } catch (error) {
      console.error("Erro ao exportar pessoas:", error);
      toast_error('Erro ao exportar pessoas');
    } finally {
      setIsExporting(false);
    }
  }, [search, personsFilter, statusFilter, relationshipFilter, companyFilter, companies, toast_success, toast_error]);

  // Loading states
  if (isLoadingPersons && persons.length === 0) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Pessoas"
          description="Gerencie pacientes, contatos e informações pessoais."
          moduleColor="people"
        />
        <TableLoadingSkeleton rows={8} columns={6} />
      </div>
    );
  }

  // Error state
  if (personsError) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Pessoas"
          description="Gerencie pacientes, contatos e informações pessoais."
          moduleColor="people"
        />
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            Erro ao carregar dados. Tente novamente.
          </div>
          <button
            onClick={refetchPersons}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Gerenciamento de Pessoas"
        description="Gerencie pacientes, contatos e informações pessoais."
        moduleColor="people"
        actions={
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setPersonFormOpen(true)}
              className="inline-flex items-center px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Nova Pessoa
            </button>
          </div>
        }
      />

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <ModuleFormGroup label="Buscar">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  placeholder="Nome, email, telefone..."
                  className="pl-10 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </ModuleFormGroup>

            {/* Status Filter */}
            <ModuleFormGroup label="Status">
              <ModuleSelect
                value={statusFilter}
                onChange={(e) => handleStatusFilterChange(e.target.value)}
                moduleColor="people"
              >
                <option value="">Todos</option>
                <option value="active">Ativos</option>
                <option value="inactive">Inativos</option>
              </ModuleSelect>
            </ModuleFormGroup>

            {/* Company Filter */}
            {currentUser?.role === "SYSTEM_ADMIN" && (
              <ModuleFormGroup label="Empresa">
                <ModuleSelect
                  value={companyFilter}
                  onChange={(e) => handleCompanyFilterChange(e.target.value)}
                  moduleColor="people"
                  disabled={isLoadingAuxiliary}
                >
                  <option value="">Todas</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </ModuleFormGroup>
            )}

            {/* Actions */}
            <div className="flex items-end space-x-2">
              <button
                type="submit"
                className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
              >
                <Search className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={handleResetFilters}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <ModuleTable
          data={persons}
          columns={[
            { key: 'fullName', label: 'Nome', sortable: true },
            { key: 'email', label: 'Email' },
            { key: 'phone', label: 'Telefone' },
            { key: 'relationship', label: 'Relacionamento' },
            { key: 'active', label: 'Status', render: (value) => (
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {value ? 'Ativo' : 'Inativo'}
              </span>
            )},
            { key: 'actions', label: 'Ações', render: (_, person) => (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => router.push(`/dashboard/people/persons/${person.id}`)}
                  className="p-1 text-blue-600 hover:text-blue-800"
                  title="Ver detalhes"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEditPerson(person)}
                  className="p-1 text-green-600 hover:text-green-800"
                  title="Editar"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleToggleStatus(person)}
                  className="p-1 text-yellow-600 hover:text-yellow-800"
                  title={person.active ? "Desativar" : "Ativar"}
                >
                  <Filter className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDeletePerson(person)}
                  className="p-1 text-red-600 hover:text-red-800"
                  title="Excluir"
                >
                  <Trash className="h-4 w-4" />
                </button>
              </div>
            )}
          ]}
          pagination={{
            currentPage,
            totalPages,
            totalItems: totalPersons,
            itemsPerPage: ITEMS_PER_PAGE,
            onPageChange: handlePageChange
          }}
          loading={isLoadingPersons}
          emptyMessage="Nenhuma pessoa encontrada"
        />
      </div>

      {/* Modals */}
      <PersonFormModal
        isOpen={personFormOpen}
        onClose={() => setPersonFormOpen(false)}
        person={selectedPerson}
        onSuccess={() => {
          setPersonFormOpen(false);
          setSelectedPerson(null);
          refetchPersons();
        }}
      />

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ''}
        confirmText="Confirmar"
        cancelText="Cancelar"
      />
    </div>
  );
};

export default PersonsPageOptimized; 