'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useDataCache } from './useDataCache';

// Hook para carregamento otimizado de dados
export function useOptimizedData(key, fetchFunction, options = {}) {
  const {
    params = {},
    ttl = 5 * 60 * 1000, // 5 minutos
    enabled = true,
    refetchOnMount = false,
    refetchOnWindowFocus = false,
    parallel = false, // Se deve carregar em paralelo
    dependencies = [], // Dependências para recarregar
    onSuccess = null,
    onError = null
  } = options;

  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetched, setLastFetched] = useState(null);
  const abortControllerRef = useRef(null);

  // Usar o hook de cache
  const cacheResult = useDataCache(key, fetchFunction, {
    params,
    ttl,
    enabled,
    refetchOnMount,
    refetchOnWindowFocus
  });

  // Função para buscar dados com cancelamento
  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!enabled || !fetchFunction) return;

    // Cancelar requisição anterior se existir
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Criar novo controller
    abortControllerRef.current = new AbortController();

    try {
      setIsLoading(true);
      setError(null);

      const result = await fetchFunction(params, abortControllerRef.current.signal);
      
      setData(result);
      setLastFetched(Date.now());
      
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (err) {
      if (err.name === 'AbortError') {
        return; // Ignorar erros de cancelamento
      }
      
      console.error(`Erro ao buscar dados para ${key}:`, err);
      setError(err);
      
      if (onError) {
        onError(err);
      }
    } finally {
      setIsLoading(false);
    }
  }, [key, fetchFunction, params, enabled, onSuccess, onError]);

  // Carregar dados quando dependências mudarem
  useEffect(() => {
    if (enabled) {
      fetchData(refetchOnMount);
    }
  }, [enabled, refetchOnMount, ...dependencies, fetchData]);

  // Cleanup ao desmontar
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    ...cacheResult,
    data: cacheResult.data || data,
    isLoading: cacheResult.isLoading || isLoading,
    error: cacheResult.error || error,
    lastFetched: cacheResult.lastFetched || lastFetched,
    refetch: fetchData,
    abort: () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    }
  };
}

// Hook para carregamento paralelo de múltiplos dados
export function useParallelData(queries) {
  const [results, setResults] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const abortControllersRef = useRef({});

  const fetchAllData = useCallback(async (forceRefresh = false) => {
    setIsLoading(true);
    setErrors({});

    // Cancelar todas as requisições anteriores
    Object.values(abortControllersRef.current).forEach(controller => {
      controller.abort();
    });

    const newControllers = {};
    const promises = queries.map(async ({ key, fetchFunction, params = {} }) => {
      // Criar novo controller para esta requisição
      const controller = new AbortController();
      newControllers[key] = controller;

      try {
        const result = await fetchFunction(params, controller.signal);
        return { key, data: result, error: null };
      } catch (error) {
        if (error.name === 'AbortError') {
          return { key, data: null, error: null };
        }
        return { key, data: null, error };
      }
    });

    try {
      const results = await Promise.all(promises);
      
      const newResults = {};
      const newErrors = {};
      
      results.forEach(({ key, data, error }) => {
        if (error) {
          newErrors[key] = error;
        } else {
          newResults[key] = data;
        }
      });

      setResults(newResults);
      setErrors(newErrors);
    } catch (error) {
      console.error('Erro no carregamento paralelo:', error);
    } finally {
      setIsLoading(false);
    }

    // Atualizar controllers
    abortControllersRef.current = newControllers;
  }, [queries]);

  // Carregar dados na montagem
  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // Cleanup ao desmontar
  useEffect(() => {
    return () => {
      Object.values(abortControllersRef.current).forEach(controller => {
        controller.abort();
      });
    };
  }, []);

  return {
    results,
    isLoading,
    errors,
    refetch: fetchAllData,
    abort: () => {
      Object.values(abortControllersRef.current).forEach(controller => {
        controller.abort();
      });
    }
  };
}

// Hook para carregamento progressivo
export function useProgressiveData(key, fetchFunction, options = {}) {
  const {
    params = {},
    ttl = 5 * 60 * 1000,
    enabled = true,
    stages = ['basic', 'detailed', 'complete'], // Estágios de carregamento
    stageParams = {} // Parâmetros específicos para cada estágio
  } = options;

  const [currentStage, setCurrentStage] = useState(0);
  const [stageData, setStageData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  const loadStage = useCallback(async (stageIndex) => {
    if (stageIndex >= stages.length) return;

    const stage = stages[stageIndex];
    const stageParam = stageParams[stage] || {};

    try {
      setIsLoading(true);
      setError(null);

      const result = await fetchFunction({
        ...params,
        ...stageParam,
        stage
      });

      setStageData(prev => ({
        ...prev,
        [stage]: result
      }));

      setCurrentStage(stageIndex + 1);
      setProgress(((stageIndex + 1) / stages.length) * 100);

      // Carregar próximo estágio automaticamente
      if (stageIndex + 1 < stages.length) {
        setTimeout(() => loadStage(stageIndex + 1), 100);
      }
    } catch (err) {
      console.error(`Erro ao carregar estágio ${stage}:`, err);
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, [stages, stageParams, params, fetchFunction]);

  // Iniciar carregamento progressivo
  useEffect(() => {
    if (enabled) {
      loadStage(0);
    }
  }, [enabled, loadStage]);

  return {
    data: stageData,
    currentStage: stages[currentStage - 1],
    isLoading,
    error,
    progress,
    refetch: () => {
      setCurrentStage(0);
      setStageData({});
      setProgress(0);
      loadStage(0);
    }
  };
}

// Hook para carregamento com retry
export function useRetryData(key, fetchFunction, options = {}) {
  const {
    params = {},
    ttl = 5 * 60 * 1000,
    enabled = true,
    maxRetries = 3,
    retryDelay = 1000,
    retryBackoff = 2
  } = options;

  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const retryTimeoutRef = useRef(null);

  const fetchWithRetry = useCallback(async (attempt = 0) => {
    if (!enabled || !fetchFunction) return;

    try {
      setIsLoading(true);
      setError(null);

      const result = await fetchFunction(params);
      
      setData(result);
      setRetryCount(0);
    } catch (err) {
      console.error(`Erro na tentativa ${attempt + 1} para ${key}:`, err);
      
      if (attempt < maxRetries) {
        setRetryCount(attempt + 1);
        
        const delay = retryDelay * Math.pow(retryBackoff, attempt);
        
        retryTimeoutRef.current = setTimeout(() => {
          fetchWithRetry(attempt + 1);
        }, delay);
      } else {
        setError(err);
        setRetryCount(0);
      }
    } finally {
      setIsLoading(false);
    }
  }, [key, fetchFunction, params, enabled, maxRetries, retryDelay, retryBackoff]);

  // Carregar dados na montagem
  useEffect(() => {
    if (enabled) {
      fetchWithRetry();
    }
  }, [enabled, fetchWithRetry]);

  // Cleanup ao desmontar
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    data,
    isLoading,
    error,
    retryCount,
    refetch: () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      fetchWithRetry();
    }
  };
}

export default useOptimizedData; 