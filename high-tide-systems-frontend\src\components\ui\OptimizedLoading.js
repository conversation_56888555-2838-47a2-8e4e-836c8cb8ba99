'use client';

import React from 'react';
import { RefreshCw, Loader2 } from 'lucide-react';

// Componente de loading simples
export const LoadingSpinner = ({ 
  size = 'md', 
  color = 'primary',
  className = '',
  text = 'Carregando...'
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  const colorClasses = {
    primary: 'border-primary-500',
    secondary: 'border-gray-500',
    white: 'border-white',
    orange: 'border-orange-500'
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className={`animate-spin rounded-full border-2 border-t-transparent ${sizeClasses[size]} ${colorClasses[color]}`} />
      {text && (
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{text}</p>
      )}
    </div>
  );
};

// Componente de loading com skeleton
export const LoadingSkeleton = ({ 
  lines = 3, 
  className = '',
  height = 'h-4'
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={`${height} bg-gray-200 dark:bg-gray-700 rounded animate-pulse`}
          style={{
            animationDelay: `${index * 0.1}s`
          }}
        />
      ))}
    </div>
  );
};

// Componente de loading para tabelas
export const TableLoadingSkeleton = ({ 
  rows = 5, 
  columns = 4,
  className = ''
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {/* Header skeleton */}
      <div className="flex space-x-3">
        {Array.from({ length: columns }).map((_, index) => (
          <div
            key={`header-${index}`}
            className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse flex-1"
            style={{
              animationDelay: `${index * 0.05}s`
            }}
          />
        ))}
      </div>
      
      {/* Rows skeleton */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={`row-${rowIndex}`} className="flex space-x-3">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={`cell-${rowIndex}-${colIndex}`}
              className="h-4 bg-gray-100 dark:bg-gray-800 rounded animate-pulse flex-1"
              style={{
                animationDelay: `${(rowIndex * columns + colIndex) * 0.05}s`
              }}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

// Componente de loading para cards
export const CardLoadingSkeleton = ({ 
  cards = 3,
  className = ''
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {Array.from({ length: cards }).map((_, index) => (
        <div
          key={index}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div className="space-y-4">
            {/* Title skeleton */}
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4" />
            
            {/* Content skeleton */}
            <div className="space-y-2">
              <div className="h-4 bg-gray-100 dark:bg-gray-800 rounded animate-pulse" />
              <div className="h-4 bg-gray-100 dark:bg-gray-800 rounded animate-pulse w-5/6" />
              <div className="h-4 bg-gray-100 dark:bg-gray-800 rounded animate-pulse w-4/6" />
            </div>
            
            {/* Button skeleton */}
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/3" />
          </div>
        </div>
      ))}
    </div>
  );
};

// Componente de loading com progresso
export const LoadingProgress = ({ 
  progress = 0,
  text = 'Carregando...',
  className = ''
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <span className="text-sm text-gray-600 dark:text-gray-400">{text}</span>
        <span className="text-sm font-medium text-gray-900 dark:text-white">
          {Math.round(progress)}%
        </span>
      </div>
      
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          className="bg-primary-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${progress}%` }}
        />
      </div>
    </div>
  );
};

// Componente de loading para páginas inteiras
export const PageLoading = ({ 
  title = 'Carregando página...',
  subtitle = 'Aguarde um momento',
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center min-h-[400px] ${className}`}>
      <div className="text-center space-y-4">
        <div className="relative">
          <RefreshCw className="h-12 w-12 text-primary-500 animate-spin mx-auto" />
          <div className="absolute inset-0 rounded-full border-2 border-primary-200 animate-ping" />
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {subtitle}
          </p>
        </div>
        
        {/* Loading dots */}
        <div className="flex justify-center space-x-1">
          {[0, 1, 2].map((index) => (
            <div
              key={index}
              className="w-2 h-2 bg-primary-500 rounded-full animate-bounce"
              style={{
                animationDelay: `${index * 0.1}s`,
                animationDuration: '1s'
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

// Componente de loading para módulos específicos
export const ModuleLoading = ({ 
  moduleName = 'Módulo',
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center min-h-[300px] ${className}`}>
      <div className="text-center space-y-4">
        <div className="relative">
          <Loader2 className="h-10 w-10 text-primary-500 animate-spin mx-auto" />
        </div>
        
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Carregando {moduleName}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Preparando dados e configurações...
          </p>
        </div>
      </div>
    </div>
  );
};

// Componente de loading inline
export const InlineLoading = ({ 
  text = 'Carregando...',
  size = 'sm',
  className = ''
}) => {
  return (
    <div className={`inline-flex items-center space-x-2 ${className}`}>
      <Loader2 className={`animate-spin ${size === 'sm' ? 'h-4 w-4' : 'h-5 w-5'} text-primary-500`} />
      <span className="text-sm text-gray-600 dark:text-gray-400">{text}</span>
    </div>
  );
};

export default LoadingSpinner; 