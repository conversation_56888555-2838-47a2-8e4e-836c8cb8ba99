"use client";

import React, { useState, useCallback } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import { format as dateFormat, parseISO, isAfter, isBefore, isEqual } from "date-fns";
import { ptBR } from "date-fns/locale";
import ModuleHeader from "@/components/ui/ModuleHeader";
import {
  Search,
  Calendar,
  Filter,
  Eye,
  RefreshCw,
  CheckCircle,
  XCircle
} from "lucide-react";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import { AppointmentModal } from "@/components/calendar/AppointmentModal";
import AppointmentTable from "@/components/appointmentsReport/AppointmentTable";
import ReportFilters from "@/components/appointmentsReport/ReportFilter";
import ClientReportFilters from "@/components/appointmentsReport/ClientReportFilter";
import { usePermissions } from "@/hooks/usePermissions";
import { useToast } from "@/contexts/ToastContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { useOptimizedData, useParallelData } from '@/hooks/useOptimizedData';
import { LoadingSpinner, TableLoadingSkeleton } from '@/components/ui/OptimizedLoading';

// Tutorial steps for the appointments page
const getAppointmentsTutorialSteps = (isClientUser) => [
  {
    target: 'body',
    title: 'Meus Agendamentos',
    content: isClientUser
      ? 'Esta tela permite visualizar seus agendamentos e de pessoas relacionadas a você.'
      : 'Esta tela permite visualizar todos os seus agendamentos.',
    placement: 'center'
  },
  {
    target: '.filter-section',
    title: 'Filtros',
    content: isClientUser
      ? 'Use estes filtros para encontrar agendamentos específicos por data, status, paciente, local ou tipo de serviço.'
      : 'Use estes filtros para encontrar agendamentos específicos por data, status, profissional ou texto.',
    placement: 'bottom'
  },
  {
    target: '#scheduler-appointments-table',
    title: 'Tabela de Agendamentos',
    content: isClientUser
      ? 'Aqui você pode visualizar seus agendamentos e de pessoas relacionadas a você.'
      : 'Aqui você pode visualizar todos os seus agendamentos.',
    placement: 'top'
  }
];

const AppointmentsPage = () => {
  // Estado para filtros
  const [filters, setFilters] = useState({
    search: '',
    startDate: null,
    endDate: null,
    status: []
  });

  // Estado para paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Estado para controle do modal de agendamento
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);

  // Permissões - clientes só podem visualizar
  const { can, isClient } = usePermissions();
  const canView = can("scheduler.appointments.view");

  // Get the appropriate tutorial steps based on user type
  const appointmentsTutorialSteps = getAppointmentsTutorialSteps(isClient());

  // Toast notifications
  const { toast_success, toast_error } = useToast();

  // Hook otimizado para carregar agendamentos
  const {
    data: appointmentsData,
    isLoading: isLoadingAppointments,
    error: appointmentsError,
    refetch: refetchAppointments
  } = useOptimizedData(
    'appointments',
    async (params) => {
      // Para clientes, não enviamos o filtro de providers (profissionais)
      const clientSafeFilters = { ...filters };

      // Se o usuário for cliente, remover o filtro de providers
      if (isClient() && clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {
        console.log("[CLIENT-FILTER] Removendo filtro de providers para cliente");
        delete clientSafeFilters.providers;
      }

      const response = await appointmentService.getAppointments({
        limit: 1000, // Um número grande para garantir que todos os itens sejam retornados
        search: clientSafeFilters.search || undefined,
        startDate: clientSafeFilters.startDate ? dateFormat(clientSafeFilters.startDate, "yyyy-MM-dd") : undefined,
        endDate: clientSafeFilters.endDate ? dateFormat(clientSafeFilters.endDate, "yyyy-MM-dd") : undefined,
        status: clientSafeFilters.status && clientSafeFilters.status.length > 0 ? clientSafeFilters.status : undefined,
        persons: clientSafeFilters.persons && clientSafeFilters.persons.length > 0 ? clientSafeFilters.persons : undefined,
        locations: clientSafeFilters.locations && clientSafeFilters.locations.length > 0 ? clientSafeFilters.locations : undefined,
        serviceTypes: clientSafeFilters.serviceTypes && clientSafeFilters.serviceTypes.length > 0 ? clientSafeFilters.serviceTypes : undefined,
      });

      return response;
    },
    {
      params: {
        filters,
        isClient: isClient()
      },
      ttl: 1 * 60 * 1000, // 1 minuto (agendamentos mudam frequentemente)
      refetchOnMount: false,
      dependencies: [filters, isClient()]
    }
  );

  // Extrair dados
  const appointments = appointmentsData?.appointments || [];
  const totalAppointments = appointments.length;
  const totalPages = Math.ceil(totalAppointments / itemsPerPage);

  // Aplicar filtros locais
  const applyLocalFilters = useCallback(() => {
    let filtered = [...appointments];

    // Aplicar filtro de texto
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(appointment =>
        (appointment.title && appointment.title.toLowerCase().includes(searchLower)) ||
        (appointment.description && appointment.description.toLowerCase().includes(searchLower)) ||
        (appointment.provider?.name && appointment.provider.name.toLowerCase().includes(searchLower)) ||
        (appointment.person?.name && appointment.person.name.toLowerCase().includes(searchLower)) ||
        (appointment.location?.name && appointment.location.name.toLowerCase().includes(searchLower))
      );
    }

    // Aplicar filtro de data inicial
    if (filters.startDate) {
      filtered = filtered.filter(appointment =>
        isAfter(new Date(appointment.startDate), new Date(filters.startDate)) ||
        isEqual(new Date(appointment.startDate), new Date(filters.startDate))
      );
    }

    // Aplicar filtro de data final
    if (filters.endDate) {
      filtered = filtered.filter(appointment =>
        isBefore(new Date(appointment.startDate), new Date(filters.endDate)) ||
        isEqual(new Date(appointment.startDate), new Date(filters.endDate))
      );
    }

    // Aplicar filtro de status
    if (filters.status.length > 0) {
      filtered = filtered.filter(appointment =>
        filters.status.includes(appointment.status)
      );
    }

    return filtered;
  }, [appointments, filters]);

  // Aplicar filtros e obter dados filtrados
  const filteredAppointments = applyLocalFilters();

  // Função para abrir o modal de visualização
  const handleViewAppointment = useCallback((appointment) => {
    setSelectedAppointment(appointment);
    setIsModalOpen(true);
  }, []);

  // Função para exportar agendamentos
  const handleExport = useCallback(async () => {
    try {
      await appointmentService.exportAppointments({
        search: filters.search || undefined,
        startDate: filters.startDate ? dateFormat(filters.startDate, "yyyy-MM-dd") : undefined,
        endDate: filters.endDate ? dateFormat(filters.endDate, "yyyy-MM-dd") : undefined,
        status: filters.status && filters.status.length > 0 ? filters.status : undefined,
        persons: filters.persons && filters.persons.length > 0 ? filters.persons : undefined,
        locations: filters.locations && filters.locations.length > 0 ? filters.locations : undefined,
        serviceTypes: filters.serviceTypes && filters.serviceTypes.length > 0 ? filters.serviceTypes : undefined,
      });

      toast_success('Exportação realizada com sucesso');
    } catch (error) {
      console.error("Erro ao exportar agendamentos:", error);
      toast_error('Erro ao exportar agendamentos');
    }
  }, [filters, toast_success, toast_error]);

  // Função para atualizar filtros
  const handleFilterChange = useCallback((newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
  }, []);

  // Função para limpar filtros
  const handleClearFilters = useCallback(() => {
    setFilters({
      search: '',
      startDate: null,
      endDate: null,
      status: []
    });
    setCurrentPage(1);
  }, []);

  // Loading states
  if (isLoadingAppointments && appointments.length === 0) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Meus Agendamentos"
          description="Visualize e gerencie seus agendamentos."
          moduleColor="scheduler"
        />
        <TableLoadingSkeleton rows={8} columns={7} />
      </div>
    );
  }

  // Error state
  if (appointmentsError) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Meus Agendamentos"
          description="Visualize e gerencie seus agendamentos."
          moduleColor="scheduler"
        />
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            Erro ao carregar agendamentos. Tente novamente.
          </div>
          <button
            onClick={refetchAppointments}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  // Renderizar ações baseadas no tipo de usuário
  const renderActions = useCallback((appointment) => {
    if (isClient()) {
      return (
        <button
          onClick={() => handleViewAppointment(appointment)}
          className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
          title="Visualizar"
        >
          <Eye size={16} />
        </button>
      );
    }

    return (
      <div className="flex items-center space-x-2">
        <button
          onClick={() => handleViewAppointment(appointment)}
          className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
          title="Visualizar"
        >
          <Eye size={16} />
        </button>
        {/* Adicionar outras ações para profissionais aqui */}
      </div>
    );
  }, [isClient, handleViewAppointment]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Meus Agendamentos"
        description="Visualize e gerencie seus agendamentos."
        moduleColor="scheduler"
        tutorialSteps={appointmentsTutorialSteps}
        tutorialName="appointments-overview"
      />

      {/* Filtros */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        {isClient() ? (
          <ClientReportFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            onClearFilters={handleClearFilters}
            onExport={handleExport}
            isLoading={isLoadingAppointments}
          />
        ) : (
          <ReportFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            onClearFilters={handleClearFilters}
            onExport={handleExport}
            isLoading={isLoadingAppointments}
          />
        )}
      </div>

      {/* Tabela de Agendamentos */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <AppointmentTable
          appointments={filteredAppointments}
          isLoading={isLoadingAppointments}
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalAppointments}
          onPageChange={setCurrentPage}
          renderActions={renderActions}
          tableId="scheduler-appointments-table"
        />
      </div>

      {/* Modal de Visualização */}
      {isModalOpen && (
        <AppointmentModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          appointment={selectedAppointment}
          onSuccess={() => {
            setIsModalOpen(false);
            setSelectedAppointment(null);
            refetchAppointments();
          }}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default AppointmentsPage;
