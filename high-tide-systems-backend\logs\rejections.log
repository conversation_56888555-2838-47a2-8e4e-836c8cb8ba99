{"timestamp":"2025-06-18 15:53:39","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Wed Jun 18 2025 15:53:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":139132928,"heapTotal":45232128,"heapUsed":43293168,"external":3406175,"arrayBuffers":132102}},"os":{"loadavg":[1.92,1.6,1.62],"uptime":4462.7},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 20:11:48","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at Socket.<anonymous> (/usr/src/app/src/socket/handlers/chatHandlers.js:53:52)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at Socket.<anonymous> (/usr/src/app/src/socket/handlers/chatHandlers.js:53:52)","rejection":true,"date":"Tue Jun 24 2025 20:11:48 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":136908800,"heapTotal":44445696,"heapUsed":43088264,"external":3387981,"arrayBuffers":113908}},"os":{"loadavg":[2.35,1.9,0.97],"uptime":484.03},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":52,"file":"/usr/src/app/src/socket/handlers/chatHandlers.js","function":null,"line":53,"method":null,"native":false}]}
{"timestamp":"2025-06-24 20:40:13","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:233:44)","error":{},"stack":"Error: The client is closed\n    at Commander._RedisClient_sendCommand (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:520:31)\n    at Commander.commandsExecutor (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:190:154)\n    at BaseClass.<computed> [as publish] (/usr/src/app/node_modules/@redis/client/dist/lib/commander.js:8:29)\n    at RedisAdapter.broadcast (/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js:473:28)\n    at BroadcastOperator.emit (/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js:169:26)\n    at updateUserStatus (/usr/src/app/src/socket/socketService.js:233:44)","rejection":true,"date":"Tue Jun 24 2025 20:40:13 GMT+0000 (Coordinated Universal Time)","process":{"pid":139,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":134008832,"heapTotal":42348544,"heapUsed":40951472,"external":3375770,"arrayBuffers":101697}},"os":{"loadavg":[1.31,1.66,1.63],"uptime":2188.65},"trace":[{"column":31,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander._RedisClient_sendCommand","line":520,"method":"_RedisClient_sendCommand","native":false},{"column":154,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.commandsExecutor","line":190,"method":"commandsExecutor","native":false},{"column":29,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/commander.js","function":"BaseClass.<computed> [as publish]","line":8,"method":"<computed> [as publish]","native":false},{"column":28,"file":"/usr/src/app/node_modules/@socket.io/redis-adapter/dist/index.js","function":"RedisAdapter.broadcast","line":473,"method":"broadcast","native":false},{"column":26,"file":"/usr/src/app/node_modules/socket.io/dist/broadcast-operator.js","function":"BroadcastOperator.emit","line":169,"method":"emit","native":false},{"column":44,"file":"/usr/src/app/src/socket/socketService.js","function":"updateUserStatus","line":233,"method":null,"native":false}]}
{"timestamp":"2025-06-24 21:45:53","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 21:45:53 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":134238208,"heapTotal":43134976,"heapUsed":41122544,"external":3383190,"arrayBuffers":109117}},"os":{"loadavg":[2.2,2.09,1.85],"uptime":6128.8},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 21:47:38","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 21:47:38 GMT+0000 (Coordinated Universal Time)","process":{"pid":139,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":179539968,"heapTotal":89534464,"heapUsed":54459536,"external":3408186,"arrayBuffers":117729}},"os":{"loadavg":[1.38,1.74,1.74],"uptime":6233.68},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 21:51:34","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 21:51:34 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":223068160,"heapTotal":132526080,"heapUsed":109319032,"external":3399302,"arrayBuffers":125229}},"os":{"loadavg":[1.42,1.12,1.45],"uptime":6469.99},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 22:21:33","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 22:21:33 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":180998144,"heapTotal":88485888,"heapUsed":54279496,"external":3558111,"arrayBuffers":284038}},"os":{"loadavg":[1.72,0.55,0.4],"uptime":8269.16},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 22:23:48","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 22:23:48 GMT+0000 (Coordinated Universal Time)","process":{"pid":139,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":140767232,"heapTotal":48640000,"heapUsed":43770152,"external":3391242,"arrayBuffers":117169}},"os":{"loadavg":[1.47,0.84,0.52],"uptime":8404.23},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 22:44:22","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 22:44:22 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":132603904,"heapTotal":41562112,"heapUsed":39877712,"external":3368700,"arrayBuffers":96675}},"os":{"loadavg":[3.27,2.18,1.58],"uptime":9638.73},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 22:45:56","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 22:45:56 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":177106944,"heapTotal":85340160,"heapUsed":52815680,"external":3399434,"arrayBuffers":125361}},"os":{"loadavg":[2.2,2.07,1.59],"uptime":9732.23},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 22:48:48","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 22:48:48 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":201498624,"heapTotal":109981696,"heapUsed":77929264,"external":3612830,"arrayBuffers":338757}},"os":{"loadavg":[1.4,1.49,1.43],"uptime":9904.24},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 22:49:47","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 22:49:47 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":171040768,"heapTotal":82980864,"heapUsed":51341712,"external":3469404,"arrayBuffers":195331}},"os":{"loadavg":[2.4,1.77,1.53],"uptime":9963.32},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 23:00:13","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 23:00:13 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":167092224,"heapTotal":78786560,"heapUsed":53847368,"external":3621738,"arrayBuffers":347665}},"os":{"loadavg":[1.96,0.95,1.07],"uptime":10589.47},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
{"timestamp":"2025-06-24 23:15:14","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:305:22)\n    at process.emit (node:events:519:28)","rejection":true,"date":"Tue Jun 24 2025 23:15:14 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":154660864,"heapTotal":63582208,"heapUsed":47154408,"external":3468930,"arrayBuffers":194857}},"os":{"loadavg":[1.99,0.77,0.66],"uptime":11489.92},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":305,"method":null,"native":false},{"column":28,"file":"node:events","function":"process.emit","line":519,"method":"emit","native":false}]}
