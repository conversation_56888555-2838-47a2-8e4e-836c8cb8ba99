// src/socket/socketService.js
const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const prisma = require('../utils/prisma');

// Importar o adaptador Redis com tratamento de erro
let createAdapter;
try {
  const redisAdapter = require('@socket.io/redis-adapter');
  createAdapter = redisAdapter.createAdapter;
  console.log('[Socket.IO] Redis adapter loaded successfully');
} catch (error) {
  console.warn('[Socket.IO] Redis adapter not available, falling back to in-memory adapter');
  createAdapter = null;
}

// Handlers de eventos
const chatHandlers = require('./handlers/chatHandlers');

let io;
let redisClient;

/**
 * Inicializa o servidor Socket.IO
 * @param {Object} server - Servidor HTTP
 * @param {Object} redis - Cliente Redis
 */
const initialize = async (server, redis) => {
  try {
    redisClient = redis;

    // Configurar handler de reconexão do Redis
    redisClient.on('error', async (error) => {
      console.error('[Socket.IO] Redis error:', error);
      try {
        await redisClient.connect();
        console.log('[Socket.IO] Redis reconnected after error');
      } catch (reconnectError) {
        console.error('[Socket.IO] Failed to reconnect Redis after error:', reconnectError);
      }
    });

    redisClient.on('end', async () => {
      console.warn('[Socket.IO] Redis connection ended, attempting to reconnect...');
      try {
        await redisClient.connect();
        console.log('[Socket.IO] Redis reconnected after connection end');
      } catch (reconnectError) {
        console.error('[Socket.IO] Failed to reconnect Redis after connection end:', reconnectError);
      }
    });

    // Criar servidor Socket.IO
    io = new Server(server, {
      cors: {
        origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
        methods: ['GET', 'POST'],
        credentials: true
      },
      path: '/socket.io',
      transports: ['websocket', 'polling']
    });

    // Configurar adaptador Redis para escalar horizontalmente
    if (createAdapter && redisClient) {
      try {
        // Verificar se o Redis está conectado
        if (!redisClient.isOpen) {
          console.warn('[Socket.IO] Redis client is not open, attempting to reconnect');
          try {
            await redisClient.connect();
            console.log('[Socket.IO] Redis client reconnected successfully');
          } catch (reconnectError) {
            console.error('[Socket.IO] Failed to reconnect Redis client:', reconnectError.message);
          }
        }

        // Só prosseguir se o Redis estiver conectado e tiver o método duplicate
        if (redisClient.isOpen && redisClient.duplicate) {
          const pubClient = redisClient.duplicate();
          const subClient = redisClient.duplicate();

          io.adapter(createAdapter(pubClient, subClient));
          console.log('[Socket.IO] Redis adapter configured');
        } else {
          console.warn('[Socket.IO] Redis client is not available for adapter, using in-memory adapter');
        }
      } catch (error) {
        console.warn('[Socket.IO] Failed to configure Redis adapter:', error.message);
        console.log('[Socket.IO] Using default in-memory adapter');
      }
    } else {
      console.log('[Socket.IO] Redis adapter not available, using in-memory adapter');
    }

    // Middleware de autenticação
    io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token ||
                      socket.handshake.headers.authorization?.split(' ')[1];

        if (!token) {
          return next(new Error('Authentication error: Token not provided'));
        }

        // Verificar se o token está na lista negra
        const { isTokenBlacklisted } = require('../middlewares/auth');
        const isBlacklisted = await isTokenBlacklisted(token);
        if (isBlacklisted) {
          console.log('[Socket.IO] Token na lista negra, rejeitando conexão');
          return next(new Error('Authentication error: Token revoked'));
        }

        // Verificar token JWT
        console.log('[Socket.IO] Verificando token JWT...');
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        console.log('[Socket.IO] Token decodificado:', decoded);

        // Buscar usuário no banco de dados - suporta tanto id quanto userId no token
        const userId = decoded.id || decoded.userId;
        console.log('[Socket.IO] ID do usuário encontrado no token:', userId);

        if (!userId) {
          console.error('[Socket.IO] Token não contém ID do usuário');
          return next(new Error('Authentication error: Invalid token format'));
        }

        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            fullName: true,
            email: true,
            role: true,
            companyId: true,
            branchId: true,
            active: true
          }
        });

        if (!user || !user.active) {
          return next(new Error('Authentication error: User not found or inactive'));
        }

        // Armazenar dados do usuário no objeto socket
        socket.user = user;

        // Adicionar socket a salas específicas
        socket.join(`user:${user.id}`);

        if (user.companyId) {
          socket.join(`company:${user.companyId}`);
        }

        if (user.branchId) {
          socket.join(`branch:${user.branchId}`);
        }

        next();
      } catch (error) {
        console.error('[Socket.IO] Authentication error:', error);
        next(new Error('Authentication error: Invalid token'));
      }
    });

    // Configurar eventos de conexão
    io.on('connection', (socket) => {
      console.log(`[Socket.IO] User connected: ${socket.user.fullName} (${socket.user.id})`);

      // Atualizar status online do usuário
      updateUserStatus(socket.user.id, true);

      // Registrar handlers de eventos
      registerEventHandlers(socket);

      // Evento de desconexão
      socket.on('disconnect', async () => {
        console.log(`[Socket.IO] User disconnected: ${socket.user.fullName} (${socket.user.id})`);

        // Atualizar status online do usuário
        updateUserStatus(socket.user.id, false);
      });
    });

    console.log('[Socket.IO] Server initialized');
    return io;
  } catch (error) {
    console.error('[Socket.IO] Initialization error:', error);
    throw error;
  }
};

/**
 * Registra os handlers de eventos para um socket
 * @param {Object} socket - Socket do cliente
 */
const registerEventHandlers = (socket) => {
  // Registrar handlers de chat
  chatHandlers.register(socket, io);
};

/**
 * Verifica se o cliente Redis está conectado
 * @returns {boolean} - Status da conexão
 */
const isRedisConnected = () => {
  return redisClient && redisClient.isOpen;
};

/**
 * Atualiza o status online de um usuário
 * @param {string} userId - ID do usuário
 * @param {boolean} isOnline - Status online
 */
const updateUserStatus = async (userId, isOnline) => {
  try {
    // Verificar se o Redis está conectado antes de tentar usar
    if (isRedisConnected()) {
      try {
        const key = `user:online:${userId}`;
        if (isOnline) {
          await redisClient.set(key, 'true', { EX: 3600 });
        } else {
          await redisClient.del(key);
        }
      } catch (redisError) {
        // Ignorar erros do Redis para não interromper o fluxo
        console.warn('[Socket.IO] Redis error in updateUserStatus:', redisError.message);
      }
    }

    // Emitir evento de atualização de status para usuários relevantes
    // Mesmo se o Redis falhar, ainda podemos notificar os usuários conectados
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true }
      });

      if (user && user.companyId && io) {
        io.to(`company:${user.companyId}`).emit('user:status', {
          userId,
          isOnline
        });
      }
    } catch (dbError) {
      console.error('[Socket.IO] Database error in updateUserStatus:', dbError);
    }
  } catch (error) {
    console.error('[Socket.IO] Error updating user status:', error);
  }
};

/**
 * Verifica se um usuário está online
 * @param {string} userId - ID do usuário
 * @returns {Promise<boolean>} - Status online
 */
const isUserOnline = async (userId) => {
  try {
    // Verificar se o Redis está conectado antes de tentar usar
    if (isRedisConnected()) {
      try {
        const status = await redisClient.get(`user:online:${userId}`);
        return status === 'true';
      } catch (redisError) {
        // Ignorar erros do Redis e retornar false
        console.warn('[Socket.IO] Redis error in isUserOnline:', redisError.message);
        return false;
      }
    }
    return false;
  } catch (error) {
    console.error('[Socket.IO] Error checking user status:', error);
    return false;
  }
};

/**
 * Envia uma mensagem para um usuário específico
 * @param {string} userId - ID do usuário destinatário
 * @param {string} event - Nome do evento
 * @param {Object} data - Dados da mensagem
 */
const sendToUser = (userId, event, data) => {
  io.to(`user:${userId}`).emit(event, data);
};

/**
 * Envia uma mensagem para todos os usuários de uma empresa
 * @param {string} companyId - ID da empresa
 * @param {string} event - Nome do evento
 * @param {Object} data - Dados da mensagem
 */
const sendToCompany = (companyId, event, data) => {
  io.to(`company:${companyId}`).emit(event, data);
};

/**
 * Envia uma mensagem para todos os usuários de uma unidade
 * @param {string} branchId - ID da unidade
 * @param {string} event - Nome do evento
 * @param {Object} data - Dados da mensagem
 */
const sendToBranch = (branchId, event, data) => {
  io.to(`branch:${branchId}`).emit(event, data);
};

/**
 * Envia uma mensagem para todos os participantes de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} event - Nome do evento
 * @param {Object} data - Dados da mensagem
 */
const sendToConversation = (conversationId, event, data) => {
  io.to(`conversation:${conversationId}`).emit(event, data);
};

module.exports = {
  initialize,
  isUserOnline,
  sendToUser,
  sendToCompany,
  sendToBranch,
  sendToConversation,
  getIO: () => io
};
