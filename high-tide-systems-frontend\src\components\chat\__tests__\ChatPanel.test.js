import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ChatProvider } from '../../../contexts/ChatContext';
import ChatPanel from '../ChatPanel';

// Mock do contexto e dados
jest.mock('../../../contexts/ChatContext', () => {
  const original = jest.requireActual('../../../contexts/ChatContext');
  return {
    ...original,
    useChat: () => ({
      conversations: [
        { id: '1', name: '<PERSON>u<PERSON><PERSON>e', lastMessage: { content: 'Oi!' } }
      ],
      setActiveConversation: jest.fn(),
      messages: [],
      loadConversations: jest.fn(),
      isLoading: false,
      toggleChatPanel: jest.fn(),
      toggleChatModal: jest.fn(),
    })
  };
});

describe('ChatPanel', () => {
  it('deve renderizar o painel de chat e mostrar conversas', () => {
    render(
      <ChatProvider>
        <ChatPanel />
      </ChatProvider>
    );
    expect(screen.getByText('Usuário Teste')).toBeInTheDocument();
    expect(screen.getByText('Oi!')).toBeInTheDocument();
  });

  it('deve chamar toggleChatPanel ao clicar para fechar', () => {
    render(
      <ChatProvider>
        <ChatPanel />
      </ChatProvider>
    );
    const btn = screen.getByLabelText(/fechar chat/i);
    fireEvent.click(btn);
    // Aqui você pode verificar se a função mock foi chamada
  });
}); 