"use client";

import React, { useState, useMemo } from "react";
import {
  BarChart,
  Bar,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import {
  Users,
  Activity,
  Briefcase,
  Calendar,
  Clock,
  TrendingUp,
  Settings,
  Search,
  Bell,
  Zap,
  Server,
  Database,
  Shield,
  CreditCard,
  Building,
  LayoutDashboard
} from "lucide-react";
import ExportMenu from "@/components/ui/ExportMenu";
import { adminDashboardService } from "@/app/modules/admin";
import { APP_VERSION } from "@/config/appConfig";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import { ModuleHeader, ModuleSelect, ModuleInput } from "@/components/ui";
import { useOptimizedData, useParallelData } from '@/hooks/useOptimizedData';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CardLoadingSkeleton } from '@/components/ui/OptimizedLoading';

const AdminDashboard = () => {
  // Obter contexto de autenticação
  const { user, isSystemAdmin } = useAuth();

  // Estados para filtros e UI
  const [selectedPeriod, setSelectedPeriod] = useState("7dias");
  const [isExporting, setIsExporting] = useState(false);
  const [selectedCompanyId, setSelectedCompanyId] = useState(null);

  // Hook otimizado para carregar dados do dashboard
  const {
    data: dashboardData,
    isLoading: isLoadingDashboard,
    error: dashboardError,
    refetch: refetchDashboard
  } = useOptimizedData(
    'admin-dashboard',
    async (params) => {
      if (!selectedCompanyId) return null;
      
      const allData = await adminDashboardService.getAllDashboardData({
        companyId: selectedCompanyId,
      });
      return allData;
    },
    {
      params: {
        selectedCompanyId,
        selectedPeriod
      },
      ttl: 2 * 60 * 1000, // 2 minutos
      refetchOnMount: false,
      dependencies: [selectedCompanyId, selectedPeriod]
    }
  );

  // Hook para carregamento paralelo de dados auxiliares
  const {
    results: auxiliaryData,
    isLoading: isLoadingAuxiliary,
    errors: auxiliaryErrors
  } = useParallelData([
    {
      key: 'companies',
      fetchFunction: async () => {
        if (isSystemAdmin()) {
          return await companyService.getCompaniesForSelect();
        } else if (user?.companyId) {
          const company = await companyService.getCurrentCompany();
          return [company];
        }
        return [];
      }
    },
    {
      key: 'activityData',
      fetchFunction: async () => {
        if (!selectedCompanyId) return [];
        return await adminDashboardService.getActivityData(
          selectedPeriod,
          { companyId: selectedCompanyId }
        );
      }
    }
  ]);

  // Extrair dados
  const statsData = dashboardData?.stats || {
    usersTotal: 0,
    activeUsers: 0,
    clientsTotal: 0,
    appointmentsTotal: 0,
    professionsTotal: 0,
    groupsTotal: 0,
  };
  
  const growthData = dashboardData?.growth || {
    usersGrowth: 0,
    activeUsersGrowth: 0,
    clientsGrowth: 0,
    appointmentsGrowth: 0,
    professionsGrowth: 0,
    groupsGrowth: 0,
  };
  
  const activityData = auxiliaryData.activityData || [];
  const userModuleData = dashboardData?.userModuleData || [];
  const professionDistribution = dashboardData?.professionDistribution || [];
  const usersData = dashboardData?.usersData || [];
  const recentActivity = dashboardData?.recentActivity || [];
  const systemInfo = dashboardData?.systemInfo || {
    version: APP_VERSION,
    lastBackup: "Carregando...",
    securityStatus: "Carregando...",
    currentPlan: "Carregando...",
  };
  
  const companies = auxiliaryData.companies || [];
  const currentCompany = companies.find(c => c.id === user?.companyId);

  // Cores para os gráficos de pizza
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#FF6B6B", "#4CAF50", "#9C27B0"];
  const PROFESSION_COLORS = ["#FF9933", "#3B82F6", "#8B5CF6", "#10B981", "#EC4899", "#F59E0B", "#6366F1", "#EF4444"];

  // Inicializar empresa selecionada
  React.useEffect(() => {
    if (companies.length > 0 && !selectedCompanyId) {
      if (isSystemAdmin()) {
        setSelectedCompanyId(companies[0].id);
      } else if (user?.companyId) {
        setSelectedCompanyId(user.companyId);
      }
    }
  }, [companies, selectedCompanyId, isSystemAdmin, user?.companyId]);

  // Manipulador para mudança de empresa
  const handleCompanyChange = (e) => {
    setSelectedCompanyId(e.target.value);
  };

  // Função para exportar os dados do dashboard
  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Obter o nome da empresa selecionada
      let companyName = "";
      if (isSystemAdmin() && selectedCompanyId) {
        const company = companies.find(c => c.id === selectedCompanyId);
        if (company) companyName = company.name;
      } else if (currentCompany) {
        companyName = currentCompany.name;
      }

      await adminDashboardService.exportDashboardData({
        companyId: selectedCompanyId,
        period: selectedPeriod,
        companyName
      }, format);

      // Aqui você pode adicionar uma notificação de sucesso
    } catch (error) {
      console.error("Erro ao exportar dados do dashboard:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Loading states
  if (isLoadingDashboard && isLoadingAuxiliary) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Dashboard Administrativo"
          description="Visão geral do sistema e métricas importantes."
          moduleColor="admin"
        />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <CardLoadingSkeleton key={index} />
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <CardLoadingSkeleton />
          <CardLoadingSkeleton />
        </div>
      </div>
    );
  }

  // Error state
  if (dashboardError) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Dashboard Administrativo"
          description="Visão geral do sistema e métricas importantes."
          moduleColor="admin"
        />
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            Erro ao carregar dados do dashboard. Tente novamente.
          </div>
          <button
            onClick={refetchDashboard}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Dashboard Administrativo"
        description="Visão geral do sistema e métricas importantes."
        moduleColor="admin"
        actions={
          <div className="flex items-center space-x-4">
            {/* Seletor de empresa (apenas para SYSTEM_ADMIN) */}
            {isSystemAdmin() && (
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Empresa:
                </label>
                <ModuleSelect
                  value={selectedCompanyId || ""}
                  onChange={handleCompanyChange}
                  moduleColor="admin"
                  disabled={isLoadingAuxiliary}
                >
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>
            )}

            {/* Seletor de período */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Período:
              </label>
              <ModuleSelect
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                moduleColor="admin"
              >
                <option value="7dias">Últimos 7 dias</option>
                <option value="30dias">Últimos 30 dias</option>
                <option value="90dias">Últimos 90 dias</option>
                <option value="1ano">Último ano</option>
              </ModuleSelect>
            </div>

            {/* Botão de exportar */}
            <ExportMenu
              onExport={handleExport}
              isExporting={isExporting}
              disabled={isLoadingDashboard}
              className="text-admin-700 dark:text-admin-300"
            />
          </div>
        }
      />

      {/* Cards de Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total de Usuários */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total de Usuários</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{statsData.usersTotal}</p>
              <p className={`text-sm ${growthData.usersGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {growthData.usersGrowth >= 0 ? '+' : ''}{growthData.usersGrowth}% vs período anterior
              </p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
              <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        {/* Usuários Ativos */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Usuários Ativos</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{statsData.activeUsers}</p>
              <p className={`text-sm ${growthData.activeUsersGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {growthData.activeUsersGrowth >= 0 ? '+' : ''}{growthData.activeUsersGrowth}% vs período anterior
              </p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
              <Activity className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        {/* Total de Clientes */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total de Clientes</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{statsData.clientsTotal}</p>
              <p className={`text-sm ${growthData.clientsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {growthData.clientsGrowth >= 0 ? '+' : ''}{growthData.clientsGrowth}% vs período anterior
              </p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
              <Briefcase className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        {/* Total de Agendamentos */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total de Agendamentos</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{statsData.appointmentsTotal}</p>
              <p className={`text-sm ${growthData.appointmentsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {growthData.appointmentsGrowth >= 0 ? '+' : ''}{growthData.appointmentsGrowth}% vs período anterior
              </p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full">
              <Calendar className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Gráfico de Atividade */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Atividade do Sistema</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={activityData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="value" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Distribuição de Profissões */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Distribuição de Profissões</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={professionDistribution}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {professionDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={PROFESSION_COLORS[index % PROFESSION_COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Informações do Sistema */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Status do Sistema */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Status do Sistema</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Versão</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{systemInfo.version}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Último Backup</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{systemInfo.lastBackup}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Status de Segurança</span>
              <span className="text-sm font-medium text-green-600">{systemInfo.securityStatus}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Plano Atual</span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">{systemInfo.currentPlan}</span>
            </div>
          </div>
        </div>

        {/* Atividade Recente */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Atividade Recente</h3>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className={`p-2 rounded-full ${getActivityIconBg(activity.type)}`}>
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">{activity.description}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{activity.timestamp}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Funções auxiliares para ícones de atividade
function getActivityIcon(type) {
  const iconProps = { size: 16 };
  
  switch (type) {
    case 'user':
      return <Users {...iconProps} />;
    case 'appointment':
      return <Calendar {...iconProps} />;
    case 'system':
      return <Server {...iconProps} />;
    case 'security':
      return <Shield {...iconProps} />;
    default:
      return <Activity {...iconProps} />;
  }
}

function getActivityIconBg(type) {
  switch (type) {
    case 'user':
      return 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400';
    case 'appointment':
      return 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400';
    case 'system':
      return 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400';
    case 'security':
      return 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400';
    default:
      return 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400';
  }
}

export default AdminDashboard;